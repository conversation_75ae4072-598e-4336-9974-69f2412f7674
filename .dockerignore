# Git
.git
.gitignore
README.md
*.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments  
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
.pytest_cache/

# Test files
tests/
test_data/
test_output/

# Development wheels (keep only one set for Docker)
wheels/

# Temporary files
temp/
tmp/  
*.tmp

# Output files
output/
outputs/
*.docx
~$*

# Java build files (if not using Java in container)
java-flow-mining-analyzer/target/
java-flow-mining-analyzer/logs/

# IDE config
.vscode/
.idea/

# Docker
Dockerfile.offline
Dockerfile.alpine
docker-compose.yml
DOCKER.md