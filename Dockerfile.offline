# 使用官方Python 3.10镜像作为基础镜像
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8

# 配置apt源和安装系统依赖（离线优化版本）
RUN sed -i 's@http://deb.debian.org@http://mirrors.aliyun.com@g' /etc/apt/sources.list.d/debian.sources || \
    sed -i 's@http://deb.debian.org@http://mirrors.ustc.edu.cn@g' /etc/apt/sources.list || true && \
    apt-get update --fix-missing && \
    apt-get install -y --no-install-recommends --fix-missing \
        gcc \
        g++ \
        libxml2-dev \
        libxslt-dev \
        libffi-dev \
        libssl-dev \
        build-essential \
        python3-dev \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 复制本地下载的wheel文件和requirements
COPY wheels_linux/ /tmp/wheels/
COPY requirements.txt .

# 使用本地wheel文件安装依赖（完全离线，不访问任何网络）
RUN pip install --no-index --find-links /tmp/wheels -r requirements.txt

# 清理临时文件
RUN rm -rf /tmp/wheels

# 复制项目文件
COPY . .

# 创建必要的目录并设置权限
RUN mkdir -p /app/uploads /app/outputs /app/temp /app/logs /app/api/uploads /app/api/outputs /app/api/temp && \
    chmod 755 /app/uploads /app/outputs /app/temp /app/logs /app/api/uploads /app/api/outputs /app/api/temp

# 暴露端口
EXPOSE 8000

# 设置容器启动时的默认命令 - 启动Web API服务器
CMD ["python", "api/api_server.py"]