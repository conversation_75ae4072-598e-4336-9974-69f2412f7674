#!/bin/bash

# Flow Mining Analyzer Docker 部署脚本
# Author: Generated with Claude Code
# Description: 快速构建和启动 Flow Mining Analyzer Docker 容器

set -e

echo "🚀 Flow Mining Analyzer Docker 部署脚本"
echo "======================================"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 docker-compose 是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p uploads outputs logs api/uploads api/outputs api/temp

# 检查环境变量
if [ -z "$DASHSCOPE_API_KEY" ]; then
    echo "⚠️  警告: DASHSCOPE_API_KEY 环境变量未设置"
    echo "   请设置此变量以启用AI功能: export DASHSCOPE_API_KEY=your_api_key"
fi

# 选择构建模式
echo ""
echo "请选择构建模式:"
echo "1) 在线构建 (Debian基础，需要网络连接)"
echo "2) 离线构建 (使用本地wheel包，需要系统依赖)"
echo "3) Alpine构建 (轻量级，适合网络不稳定环境)"
echo "4) 简单离线构建 (纯离线，最小依赖)"
echo "5) 仅启动服务 (使用已有镜像)"
echo ""
read -p "请输入选择 (1/2/3/4/5): " choice

case $choice in
    1)
        echo "🔨 开始在线构建..."
        docker build -t flow-mining-analyzer:latest .
        ;;
    2)
        echo "🔨 开始离线构建..."
        docker build -f Dockerfile.offline -t flow-mining-analyzer:latest .
        ;;
    3)
        echo "🔨 开始Alpine构建..."
        docker build -f Dockerfile.alpine -t flow-mining-analyzer:latest .
        ;;
    4)
        echo "🔨 开始简单离线构建..."
        docker build -f Dockerfile.simple -t flow-mining-analyzer:latest .
        ;;
    5)
        echo "⏭️  跳过构建，直接启动服务..."
        ;;
    *)
        echo "❌ 无效选择，退出"
        exit 1
        ;;
esac

# 启动服务
echo "🚀 启动服务..."
if command -v docker-compose &> /dev/null; then
    docker-compose up -d
else
    docker compose up -d
fi

echo ""
echo "✅ 部署完成！"
echo ""
echo "🌐 服务访问地址:"
echo "   Web界面: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
echo "   健康检查: http://localhost:8000/health"
echo ""
echo "📋 常用命令:"
echo "   查看日志: docker logs flow-mining-analyzer"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo "   查看状态: docker-compose ps"
echo ""
echo "📁 数据目录:"
echo "   上传文件: ./uploads/"
echo "   输出文件: ./outputs/"
echo "   日志文件: ./logs/"