import logging
import re
from typing import Dict, List, Any, Optional, Tuple

from src.ai.client import AIClient
from src.ai.prompts import PromptTemplates
from src.data.data_formatter import DataFormatter
from config.ai_config import DEFAULT_DOMAIN

logger = logging.getLogger(__name__)

class ContentGenerator:
    """内容生成器，使用AI模型生成分析内容"""
    
    def __init__(self, ai_client: Optional[AIClient] = None, domain: Optional[str] = DEFAULT_DOMAIN):
        """
        初始化内容生成器
        
        参数:
            ai_client: AI客户端实例，如果为None则创建新实例
            domain: 领域名称，用于在提示中提供领域上下文
        """
        self.ai_client = ai_client or AIClient()
        self.domain = domain
        logger.info(f"内容生成器初始化完成，使用领域: {domain if domain else '通用'}")
    
    def _clean_content(self, content: str) -> str:
        """
        清理内容中的多余格式标记符号并统一编号格式为点格式
        
        参数:
            content: 原始内容
                
        返回:
            清理后的内容
        """
        # 移除井号标记（####）
        content = re.sub(r'#+\s+', '', content)
        
        # 移除破折号标记（---）
        content = re.sub(r'\n\s*---\s*\n', '\n\n', content)
        
        # 处理无序列表项，保留文本但移除前面的破折号或星号
        content = re.sub(r'\n\s*[-*]\s+', '\n', content)
        
        # 移除星号强调（**文本**）保留文本内容
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)
        
        # 移除单星号强调（*文本*）保留文本内容
        content = re.sub(r'(?<!\*)\*(?!\*)(.*?)(?<!\*)\*(?!\*)', r'\1', content)
        
        # 移除反引号代码块（`文本`）保留文本内容
        content = re.sub(r'`(.*?)`', r'\1', content)
        
        # 移除三重反引号代码块
        content = re.sub(r'```.*?\n(.*?)```', r'\1', content, flags=re.DOTALL)
        
        # 移除标题中的特殊符号（如**标题**）
        content = re.sub(r'^(\d+、)\s*\*\*(.*?)\*\*', r'\1 \2', content, flags=re.MULTILINE)
        content = re.sub(r'^(\d+、)\s*\*(.*?)\*', r'\1 \2', content, flags=re.MULTILINE)
        
        # 移除标题开头和结尾的星号和其他特殊符号
        content = re.sub(r'^(\d+、)\s*[*#@]+\s*(.*?)\s*[*#@]+', r'\1 \2', content, flags=re.MULTILINE)
        
        # 移除所有空的标记后的额外空行
        content = re.sub(r'\n{3,}', '\n\n', content)
        
        # 移除段落开头的空格
        content = re.sub(r'\n\s+', '\n', content)
        
        # 替换编号格式为点格式 - 处理(1)格式
        content = re.sub(r'\(\d+\)\s*', '· ', content)
        
        # 替换编号格式为点格式 - 处理（1）格式（中文括号）
        content = re.sub(r'（\d+）\s*', '· ', content)
        
        # 确保行首的编号也被替换
        content = re.sub(r'^\(\d+\)\s*', '· ', content, flags=re.MULTILINE)
        content = re.sub(r'^（\d+）\s*', '· ', content, flags=re.MULTILINE)
        
        return content.strip()
    
    def _fix_numbering(self, content: str) -> str:
        """
        进一步处理内容中的编号，确保所有内容点使用点格式
        
        参数:
            content: 原始内容
            
        返回:
            修复编号后的内容
        """
        lines = content.split('\n')
        result_lines = []
        
        for line in lines:
            # 检查是否是一级小标题（数字、标题格式）
            if re.match(r'^\d+、', line):
                result_lines.append(line)
            # 检查是否是带编号的内容点，将其转换为点格式
            elif re.match(r'^\s*（\d+）', line) or re.match(r'^\s*\(\d+\)', line):
                # 替换为点格式
                new_line = re.sub(r'^\s*（\d+）|\s*\(\d+\)', '· ', line)
                result_lines.append(new_line)
            # 如果已经是点格式，确保格式一致
            elif line.strip().startswith('·'):
                # 确保点符号后有空格
                new_line = re.sub(r'^·\s*', '· ', line.strip())
                result_lines.append(new_line)
            else:
                result_lines.append(line)
        
        return '\n'.join(result_lines)
    
    def generate_module_content(self, module: Dict[str, Any], polished_title: str) -> str:
        """
        为单个模块生成分析内容
        
        参数:
            module: 模块数据
            polished_title: 润色后的标题
            
        返回:
            生成的分析内容
        """
        logger.info(f"为模块生成内容: '{polished_title}'")
        
        # 提取提示内容
        prompt_content = module.get("prompt", "")
        
        # 获取数据摘要
        data_summary = DataFormatter.get_data_summary(module)
        
        # 生成提示
        prompt = PromptTemplates.analyze_module(polished_title, prompt_content, data_summary, self.domain)
        
        # 获取AI响应
        response = self.ai_client.generate_completion(
            prompt=prompt,
            max_tokens=1500,
            temperature=0.7
        )
        
        if not response:
            logger.warning(f"内容生成失败，使用默认内容")
            return f"无法为模块'{polished_title}'生成内容。请检查提供的数据和配置。"
        
        # 清理内容中的格式符号
        cleaned_content = self._clean_content(response)
        
        # 修复编号
        fixed_content = self._fix_numbering(cleaned_content)
        
        logger.info(f"成功为模块生成内容，长度: {len(fixed_content)}")
        return fixed_content
    
    def generate_simple_modules_content(self, modules: List[Dict[str, Any]]) -> Tuple[str, str]:
        """
        为简单模块集合生成标题和内容
        
        参数:
            modules: 简单模块列表
            
        返回:
            (title, content)元组
        """
        if not modules:
            logger.warning("无简单模块，跳过生成")
            return "关键指标概览", "无可用数据"
        
        logger.info(f"为{len(modules)}个简单模块生成内容")
        
        # 生成标题
        title_prompt = PromptTemplates.generate_simple_title(modules, self.domain)
        title_response = self.ai_client.generate_completion(
            prompt=title_prompt,
            max_tokens=50,
            temperature=0.7
        )
        
        title = title_response or "关键指标概览"
        logger.info(f"为简单模块生成标题: '{title}'")
        
        # 生成内容
        content_prompt = PromptTemplates.analyze_simple_modules(modules, self.domain)
        content_response = self.ai_client.generate_completion(
            prompt=content_prompt,
            max_tokens=1500,
            temperature=0.7
        )
        
        content = content_response or f"无法为{len(modules)}个KPI指标生成分析内容。"
        
        # 清理内容中的格式符号
        cleaned_content = self._clean_content(content)
        
        # 修复编号
        fixed_content = self._fix_numbering(cleaned_content)
        
        logger.info(f"成功为简单模块生成内容，长度: {len(fixed_content)}")
        
        return title, fixed_content
    
    def generate_report_summary(self, module_titles: List[str], normalized_data: List[Dict[str, Any]] = None) -> str:
        """
        生成报告摘要

        参数:
            module_titles: 报告中的模块标题列表
            normalized_data: 标准化的数据列表，用于详细分析

        返回:
            生成的报告摘要
        """
        if not module_titles:
            return "无可用数据用于生成报告摘要。"

        # 构建新的提示词逻辑
        prompt = self._build_enhanced_summary_prompt(module_titles, normalized_data)

        # 打印发给大模型的提示词
        logger.info("=" * 80)
        logger.info("发送给大模型的提示词:")
        logger.info("=" * 80)
        logger.info(prompt)
        logger.info("=" * 80)

        response = self.ai_client.generate_completion(
            prompt=prompt,
            max_tokens=800,  # 增加token数量以支持更详细的分析
            temperature=0.6
        )

        if not response:
            return "无法生成报告摘要。"

        # 清理内容中的格式符号
        cleaned_summary = self._clean_content(response)

        # 修复编号
        fixed_summary = self._fix_numbering(cleaned_summary)

        return fixed_summary

    def _build_enhanced_summary_prompt(self, module_titles: List[str], normalized_data: List[Dict[str, Any]] = None) -> str:
        """
        构建增强的摘要提示词

        参数:
            module_titles: 模块标题列表
            normalized_data: 标准化数据

        返回:
            构建的提示词
        """
        # 基础分析角度
        analysis_angles = """如下所示请从下面的几个分析角度总结数据

1. 请分析数据 xxx组件中有没有异常值
2. 某时间处理时间特别长
3. 某些处理的 2 次返工次数特别大

要求给出3条总结。
下面是要分析的数据。"""

        # 格式化数据内容
        formatted_data = ""
        if normalized_data:
            for i, module in enumerate(normalized_data, 1):
                title = module.get('title', f'未命名模块{i}')
                formatted_data += f"\n{i}. 以下关于 {title} 的json数据:\n\n"
                formatted_data += "{\n"

                # 添加主要字段
                for key, value in module.items():
                    if key == 'data' and isinstance(value, list) and len(value) > 3:
                        # 如果数据太多，只显示前3行
                        formatted_data += f'    "{key}": [\n'
                        for j, row in enumerate(value[:3]):
                            formatted_data += f'        {row}'
                            if j < min(2, len(value) - 1):
                                formatted_data += ','
                            formatted_data += '\n'
                        if len(value) > 3:
                            formatted_data += f'        // ... 还有{len(value) - 3}行数据\n'
                        formatted_data += '    ],\n'
                    else:
                        # 格式化其他字段
                        if isinstance(value, str):
                            formatted_data += f'    "{key}": "{value}",\n'
                        else:
                            formatted_data += f'    "{key}": {value},\n'

                formatted_data = formatted_data.rstrip(',\n') + '\n'
                formatted_data += "}\n"
        else:
            # 如果没有详细数据，使用模块标题
            modules_info = "\n".join([f"- {title}" for title in module_titles])
            formatted_data = f"\n报告包含的模块:\n{modules_info}"

        # 构建完整提示词
        full_prompt = f"""{analysis_angles}

{formatted_data}

请基于以上数据进行深度分析，重点关注：
1. 数据中的异常值识别和分析
2. 处理时间异常长的情况分析
3. 返工次数过多的问题分析

要求：
- 必须给出3条具体的总结
- 每条总结要有明确的数据支撑
- 分析要深入具体，不要泛泛而谈
- 如果某个分析角度在当前数据中不适用，请说明原因并提供替代分析角度"""

        return full_prompt