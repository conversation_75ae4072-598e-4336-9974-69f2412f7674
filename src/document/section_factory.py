from typing import Dict, Any, List, Optional
import logging

from src.document.docx_builder import Docx<PERSON>uilder
from src.ai.content_generator import ContentGenerator
from src.ai.title_polisher import TitlePolisher
from config.ai_config import DEFAULT_DOMAIN

logger = logging.getLogger(__name__)

class SectionFactory:
    """部分工厂，协调文档部分的创建和添加"""
    
    def __init__(
        self, 
        docx_builder: DocxBuilder,
        content_generator: ContentGenerator,
        title_polisher: TitlePolisher
    ):
        """初始化部分工厂"""
        self.docx_builder = docx_builder
        self.content_generator = content_generator
        self.title_polisher = title_polisher
        self.section_titles = []
        logger.info("部分工厂初始化完成")
    
    def process_regular_module(self, module: Dict[str, Any], level: int = 1):
        """
        处理常规模块并添加到文档
        
        参数:
            module: 模块数据
            level: 标题级别 (1-3)
        """
        # 提取标题
        original_title = module.get("title", "未命名模块")
        
        # 润色标题
        polished_title = self.title_polisher.polish(original_title)
        
        # 生成内容
        content = self.content_generator.generate_module_content(module, polished_title)
        
        # 添加到文档
        self.docx_builder.add_section(polished_title, content, level)
        
        # 检查是否有图片数据并且不是简单模块
        if "image" in module and module["image"] and not module.get("simple", False):
            # 获取base64图片数据
            image_data = module["image"]
            
            # 检查是否是实际的base64数据
            if isinstance(image_data, str) and "data:image" in image_data:
                # 添加图片到文档
                self.docx_builder.add_image_from_base64(image_data)
                logger.info(f"为模块'{polished_title}'添加图片")
        
        # 记录标题用于摘要
        self.section_titles.append(polished_title)
        
        logger.info(f"处理常规模块: {original_title} -> {polished_title}")
    
    def process_simple_modules(self, modules: List[Dict[str, Any]]):
        """处理简单模块集合并添加到文档"""
        if not modules:
            logger.warning("没有简单模块可处理")
            return
        
        # 生成标题和内容
        title, content = self.content_generator.generate_simple_modules_content(modules)
        
        # 添加到文档
        self.docx_builder.add_section(title, content, 1)
        
        # 记录标题用于摘要
        self.section_titles.append(title)
        
        logger.info(f"处理{len(modules)}个简单模块，标题: {title}")
    
    def add_report_summary(self):
        """添加报告摘要（确保是最后一个章节）"""
        if not self.section_titles:
            logger.warning("没有部分标题，跳过摘要生成")
            return
        
        # 生成摘要
        summary = self.content_generator.generate_report_summary(self.section_titles)
        
        # 添加到文档
        self.docx_builder.add_executive_summary(summary)
        
        logger.info("添加报告摘要")
    
    def finalize_document(self) -> str:
        """完成文档并保存"""
        try:
            # 添加附录（如果需要）
            # self.docx_builder.add_appendix()
            
            # 保存文档
            output_path = self.docx_builder.save()
            logger.info(f"文档完成并保存到: {output_path}")
            
            return output_path
        except Exception as e:
            logger.error(f"完成文档失败: {str(e)}")
            raise